"use client";

import { useState } from "react";
import {
  Award,
  Users,
  TrendingUp,
  Clock,
  Target,
  Star,
  Activity,
  BarChart3
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  PieChart,
  Pie,
  Cell,
  Legend,
  ComposedChart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar, FilterPeriod } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for branch teller performance
const dummyTellerPerformance = [
  { 
    tellerId: "T-001", 
    name: "Alice Johnson", 
    transactions: 485, 
    volume: 125000, 
    avgTransaction: 258, 
    customerSatisfaction: 4.8,
    avgServiceTime: 3.2,
    accuracy: 99.2,
    rank: 1
  },
  { 
    tellerId: "T-002", 
    name: "Bob Smith", 
    transactions: 452, 
    volume: 118000, 
    avgTransaction: 261, 
    customerSatisfaction: 4.6,
    avgServiceTime: 3.8,
    accuracy: 98.8,
    rank: 2
  },
  { 
    tellerId: "T-003", 
    name: "Carol Davis", 
    transactions: 428, 
    volume: 108000, 
    avgTransaction: 252, 
    customerSatisfaction: 4.5,
    avgServiceTime: 4.1,
    accuracy: 98.5,
    rank: 3
  },
  { 
    tellerId: "T-004", 
    name: "David Wilson", 
    transactions: 395, 
    volume: 98000, 
    avgTransaction: 248, 
    customerSatisfaction: 4.3,
    avgServiceTime: 4.5,
    accuracy: 98.1,
    rank: 4
  },
  { 
    tellerId: "T-005", 
    name: "Eva Brown", 
    transactions: 368, 
    volume: 89000, 
    avgTransaction: 242, 
    customerSatisfaction: 4.2,
    avgServiceTime: 4.8,
    accuracy: 97.8,
    rank: 5
  }
];

const dummyTellerTrends = [
  { month: "Jan", alice: 465, bob: 435, carol: 410, david: 380, eva: 355 },
  { month: "Feb", alice: 475, bob: 445, carol: 420, david: 385, eva: 360 },
  { month: "Mar", alice: 480, bob: 450, carol: 425, david: 390, eva: 365 },
  { month: "Apr", alice: 485, bob: 452, carol: 428, david: 395, eva: 368 },
  { month: "May", alice: 490, bob: 458, carol: 432, david: 398, eva: 372 },
  { month: "Jun", alice: 485, bob: 452, carol: 428, david: 395, eva: 368 }
];

const dummyTellerMetrics = [
  { metric: "Transaction Volume", alice: 95, bob: 88, carol: 82, david: 75, eva: 68 },
  { metric: "Customer Satisfaction", alice: 96, bob: 92, carol: 90, david: 86, eva: 84 },
  { metric: "Service Speed", alice: 92, bob: 85, carol: 80, david: 75, eva: 70 },
  { metric: "Accuracy", alice: 99, bob: 99, carol: 99, david: 98, eva: 98 },
  { metric: "Reliability", alice: 98, bob: 95, carol: 92, david: 88, eva: 85 }
];

const dummyServiceTypes = [
  { type: "Cash Transactions", alice: 185, bob: 175, carol: 165, david: 155, eva: 145 },
  { type: "Account Services", alice: 125, bob: 120, carol: 115, david: 110, eva: 105 },
  { type: "Loan Processing", alice: 95, bob: 85, carol: 80, david: 75, eva: 70 },
  { type: "Customer Inquiries", alice: 80, bob: 72, carol: 68, david: 55, eva: 48 }
];

const dummyTellerSchedule = [
  { day: "Monday", alice: 8, bob: 8, carol: 6, david: 6, eva: 4 },
  { day: "Tuesday", alice: 8, bob: 8, carol: 6, david: 6, eva: 4 },
  { day: "Wednesday", alice: 8, bob: 8, carol: 6, david: 6, eva: 4 },
  { day: "Thursday", alice: 8, bob: 8, carol: 8, david: 6, eva: 6 },
  { day: "Friday", alice: 8, bob: 8, carol: 8, david: 8, eva: 6 },
  { day: "Saturday", alice: 4, bob: 4, carol: 4, david: 0, eva: 0 }
];

export default function TellerPerformancePage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Calculate KPIs from teller data
  const totalTellers = dummyTellerPerformance.length;
  const avgTransactions = Math.round(dummyTellerPerformance.reduce((sum, teller) => sum + teller.transactions, 0) / totalTellers);
  const avgSatisfaction = Math.round(dummyTellerPerformance.reduce((sum, teller) => sum + teller.customerSatisfaction, 0) / totalTellers * 10) / 10;
  const avgAccuracy = Math.round(dummyTellerPerformance.reduce((sum, teller) => sum + teller.accuracy, 0) / totalTellers * 10) / 10;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Teller Performance</h2>
        <p className="text-muted-foreground">
          Monitor and analyze teller performance metrics for branch optimization
        </p>
      </div>

      <FilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Tellers"
          value={totalTellers.toString()}
          icon={Users}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="Avg Transactions"
          value={avgTransactions.toString()}
          icon={Activity}
          trend={{ value: 8.3, isPositive: true }}
        />
        <KpiCard
          title="Avg Satisfaction"
          value={`${avgSatisfaction}/5`}
          icon={Star}
          trend={{ value: 5.2, isPositive: true }}
        />
        <KpiCard
          title="Avg Accuracy"
          value={`${avgAccuracy}%`}
          icon={Target}
          trend={{ value: 2.1, isPositive: true }}
        />
      </div>

      {/* Teller Performance Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Teller Transaction Performance">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyTellerPerformance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip />
              <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Teller Performance Trends">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummyTellerTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="alice" stroke={COLORS[0]} strokeWidth={2} name="Alice" />
              <Line type="monotone" dataKey="bob" stroke={COLORS[1]} strokeWidth={2} name="Bob" />
              <Line type="monotone" dataKey="carol" stroke={COLORS[2]} strokeWidth={2} name="Carol" />
              <Line type="monotone" dataKey="david" stroke={COLORS[3]} strokeWidth={2} name="David" />
              <Line type="monotone" dataKey="eva" stroke={COLORS[4]} strokeWidth={2} name="Eva" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Teller Metrics & Service Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Teller Performance Matrix">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={dummyTellerMetrics}>
              <PolarGrid />
              <PolarAngleAxis dataKey="metric" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar name="Alice" dataKey="alice" stroke={COLORS[0]} fill={COLORS[0]} fillOpacity={0.1} />
              <Radar name="Bob" dataKey="bob" stroke={COLORS[1]} fill={COLORS[1]} fillOpacity={0.1} />
              <Radar name="Carol" dataKey="carol" stroke={COLORS[2]} fill={COLORS[2]} fillOpacity={0.1} />
              <Tooltip />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Service Type Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyServiceTypes}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="type" 
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip />
              <Bar dataKey="alice" fill={COLORS[0]} name="Alice" />
              <Bar dataKey="bob" fill={COLORS[1]} name="Bob" />
              <Bar dataKey="carol" fill={COLORS[2]} name="Carol" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Teller Performance Table */}
      <div>
        <DataTable
          title="Detailed Teller Performance"
          columns={[
            { key: "rank", title: "Rank" },
            { key: "name", title: "Teller Name" },
            { key: "transactions", title: "Transactions" },
            { key: "volume", title: "Volume", render: (value) => formatCurrency(value) },
            { key: "avgTransaction", title: "Avg Transaction", render: (value) => formatCurrency(value) },
            { key: "customerSatisfaction", title: "Satisfaction", render: (value) => `${value}/5` },
            { key: "avgServiceTime", title: "Service Time", render: (value) => `${value} min` },
            { key: "accuracy", title: "Accuracy", render: (value) => `${value}%` },
          ]}
          data={dummyTellerPerformance}
        />
      </div>

     
    </div>
  );
}
