"use client";

import { useState } from "react";
import {
  Activity,
  Clock,
  Users,
  TrendingUp,
  AlertTriangle,
  Calendar,
  BarChart3,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  ScatterChart,
  Scatter,
  PieChart,
  Pie,
  Cell,
  Legend,
  ComposedChart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar, FilterPeriod } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for branch-specific transaction timing analysis
const dummyBranchCustomerBehavior = [
  { segment: "Daily Visitors (1-3 days)", customers: 25, avgDays: 2.1, retention: 92, branchLoyalty: 95, color: COLORS[0] },
  { segment: "Weekly Visitors (4-7 days)", customers: 45, avgDays: 5.8, retention: 85, branchLoyalty: 88, color: COLORS[1] },
  { segment: "Bi-weekly Visitors (8-14 days)", customers: 35, avgDays: 11.2, retention: 72, branchLoyalty: 75, color: COLORS[2] },
  { segment: "Monthly Visitors (15-30 days)", customers: 28, avgDays: 22.5, retention: 58, branchLoyalty: 62, color: COLORS[3] },
  { segment: "Occasional Visitors (30+ days)", customers: 15, avgDays: 45.8, retention: 35, branchLoyalty: 40, color: COLORS[4] }
];

const dummyBranchTimingTrends = [
  { month: "Jan", avgDays: 11.2, newCustomers: 15, returningCustomers: 125, churnRate: 8.5 },
  { month: "Feb", avgDays: 10.8, newCustomers: 22, returningCustomers: 135, churnRate: 7.2 },
  { month: "Mar", avgDays: 12.1, newCustomers: 18, returningCustomers: 128, churnRate: 9.1 },
  { month: "Apr", avgDays: 9.9, newCustomers: 28, returningCustomers: 145, churnRate: 6.8 },
  { month: "May", avgDays: 10.5, newCustomers: 32, returningCustomers: 155, churnRate: 7.5 },
  { month: "Jun", avgDays: 11.1, newCustomers: 25, returningCustomers: 148, churnRate: 8.2 }
];

const dummyBranchRetentionAnalysis = [
  { daysRange: "1-7 days", customers: 70, retentionRate: 88, avgLifetime: 15, branchRevenue: 185000 },
  { daysRange: "8-14 days", customers: 35, retentionRate: 72, avgLifetime: 12, branchRevenue: 125000 },
  { daysRange: "15-30 days", customers: 28, retentionRate: 58, avgLifetime: 9, branchRevenue: 95000 },
  { daysRange: "31-60 days", customers: 18, retentionRate: 45, avgLifetime: 6, branchRevenue: 65000 },
  { daysRange: "60+ days", customers: 15, retentionRate: 35, avgLifetime: 4, branchRevenue: 45000 }
];

const dummyBranchCustomerJourney = [
  { customerId: "CUST-001", avgDays: 3.2, totalTransactions: 45, totalSpend: 28000, branchLoyalty: 95, lastVisit: "2024-01-15" },
  { customerId: "CUST-045", avgDays: 5.8, totalTransactions: 38, totalSpend: 22000, branchLoyalty: 88, lastVisit: "2024-01-14" },
  { customerId: "CUST-089", avgDays: 8.1, totalTransactions: 32, totalSpend: 18000, branchLoyalty: 82, lastVisit: "2024-01-13" },
  { customerId: "CUST-123", avgDays: 12.5, totalTransactions: 25, totalSpend: 15000, branchLoyalty: 75, lastVisit: "2024-01-12" },
  { customerId: "CUST-156", avgDays: 18.2, totalTransactions: 18, totalSpend: 12000, branchLoyalty: 68, lastVisit: "2024-01-11" },
  { customerId: "CUST-178", avgDays: 25.8, totalTransactions: 12, totalSpend: 8000, branchLoyalty: 55, lastVisit: "2024-01-10" },
  { customerId: "CUST-201", avgDays: 35.5, totalTransactions: 8, totalSpend: 5000, branchLoyalty: 42, lastVisit: "2024-01-09" },
  { customerId: "CUST-234", avgDays: 48.2, totalTransactions: 5, totalSpend: 3000, branchLoyalty: 35, lastVisit: "2024-01-08" }
];

const dummyBranchPurchasingPatterns = [
  { pattern: "Regular Branch Visitors", avgDays: 4.2, avgAmount: 280, frequency: "High", branchPreference: 95 },
  { pattern: "Planned Branch Visits", avgDays: 12.8, avgAmount: 450, frequency: "Medium", branchPreference: 82 },
  { pattern: "Convenience Shoppers", avgDays: 28.5, avgAmount: 180, frequency: "Low", branchPreference: 65 },
  { pattern: "Occasional Branch Users", avgDays: 52.3, avgAmount: 320, frequency: "Very Low", branchPreference: 45 }
];

export default function BranchTransactionTimingPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Calculate KPIs from branch timing data
  const avgBranchTimeBetween = 10.8; // Average from trends
  const branchReturningCustomers = dummyBranchCustomerJourney.length;
  const branchFrequentCustomers = dummyBranchCustomerJourney.filter(customer => customer.avgDays <= 7).length;
  const avgBranchRetention = Math.round(dummyBranchRetentionAnalysis.reduce((sum, item) => sum + item.retentionRate, 0) / dummyBranchRetentionAnalysis.length);

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Time Between Transactions Analysis</h2>
        <p className="text-muted-foreground">
          Analyze the average time between transactions for returning customers at this branch
        </p>
      </div>

      <FilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Avg Time Between Visits"
          value={`${avgBranchTimeBetween} days`}
          icon={Clock}
          trend={{ value: 8.3, isPositive: false }}
        />
        <KpiCard
          title="Branch Returning Customers"
          value={branchReturningCustomers.toString()}
          icon={Users}
          trend={{ value: 12.7, isPositive: true }}
        />
        <KpiCard
          title="Frequent Branch Visitors"
          value={branchFrequentCustomers.toString()}
          icon={TrendingUp}
          trend={{ value: 15.4, isPositive: true }}
        />
        <KpiCard
          title="Branch Retention Rate"
          value={`${avgBranchRetention}%`}
          icon={Target}
          trend={{ value: 5.2, isPositive: true }}
        />
      </div>

      {/* Branch Timing Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Customer Visit Intervals">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchCustomerJourney}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="customerId"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip
                formatter={(value, name) => [
                  `${value} days`,
                  "Avg Days Between Visits"
                ]}
              />
              <Bar dataKey="avgDays" fill={COLORS[0]} name="Avg Days Between Visits" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Customer Behavior Segments">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyBranchCustomerBehavior}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ segment, customers }) => `${customers}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="customers"
              >
                {dummyBranchCustomerBehavior.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Customers"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Branch Timing Trends & Retention */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Timing Trends">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={dummyBranchTimingTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "avgDays" ? `${value} days` : 
                name === "churnRate" ? `${value}%` : value,
                name === "avgDays" ? "Avg Days" : 
                name === "churnRate" ? "Churn Rate" :
                name === "newCustomers" ? "New Customers" : "Returning Customers"
              ]} />
              <Bar dataKey="returningCustomers" fill={COLORS[1]} name="Returning Customers" />
              <Line type="monotone" dataKey="avgDays" stroke={COLORS[0]} strokeWidth={3} name="Avg Days" />
              <Line type="monotone" dataKey="churnRate" stroke={COLORS[2]} strokeWidth={3} name="Churn Rate %" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Retention by Visit Frequency">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={dummyBranchRetentionAnalysis}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="daysRange" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "retentionRate" ? `${value}%` : 
                name === "branchRevenue" ? formatCurrency(Number(value)) : value,
                name === "retentionRate" ? "Retention Rate" :
                name === "branchRevenue" ? "Branch Revenue" :
                name === "customers" ? "Customers" : "Avg Lifetime"
              ]} />
              <Area type="monotone" dataKey="retentionRate" stroke={COLORS[3]} fill={COLORS[3]} fillOpacity={0.3} name="Retention Rate %" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Branch Customer Journey Table */}
      <div>
        <DataTable
          title="Branch Customer Journey Analysis"
          columns={[
            { key: "customerId", title: "Customer ID" },
            { key: "avgDays", title: "Avg Days", render: (value) => `${value} days` },
            { key: "totalTransactions", title: "Transactions" },
            { key: "totalSpend", title: "Total Spend", render: (value) => formatCurrency(value) },
            { key: "branchLoyalty", title: "Branch Loyalty", render: (value) => `${value}%` },
            { key: "lastVisit", title: "Last Visit" },
          ]}
          data={dummyBranchCustomerJourney}
        />
      </div>

      {/* Branch Purchasing Patterns */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Branch Customer Purchasing Patterns</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Pattern Type</th>
                    <th className="text-left p-2">Avg Days Between</th>
                    <th className="text-left p-2">Avg Amount</th>
                    <th className="text-left p-2">Frequency</th>
                    <th className="text-left p-2">Branch Preference</th>
                  </tr>
                </thead>
                <tbody>
                  {dummyBranchPurchasingPatterns.map((pattern, index) => (
                    <tr key={pattern.pattern} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{pattern.pattern}</td>
                      <td className="p-2">{pattern.avgDays} days</td>
                      <td className="p-2">{formatCurrency(pattern.avgAmount)}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          pattern.frequency === 'High' ? 'bg-green-100 text-green-800' :
                          pattern.frequency === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          pattern.frequency === 'Low' ? 'bg-orange-100 text-orange-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {pattern.frequency}
                        </span>
                      </td>
                      <td className="p-2">{pattern.branchPreference}%</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Branch Timing Insights */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Branch Customer Timing Strategies</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Daily Branch Visitors (1-3 days)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Provide consistent, high-quality service experience</li>
                  <li>• Offer branch-specific loyalty rewards and recognition</li>
                  <li>• Ensure preferred teller availability during their usual times</li>
                  <li>• Monitor for any changes in visit patterns</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Weekly Branch Visitors (4-7 days)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Send personalized reminders about branch services</li>
                  <li>• Offer incentives to increase visit frequency</li>
                  <li>• Track preferred visit days and times</li>
                  <li>• Provide consistent service quality across all visits</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Infrequent Branch Visitors (15+ days)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Implement branch-specific re-engagement campaigns</li>
                  <li>• Understand barriers to more frequent branch visits</li>
                  <li>• Offer special promotions for branch services</li>
                  <li>• Consider customer satisfaction surveys</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">Branch Optimization</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Use timing data for branch-specific marketing</li>
                  <li>• Predict customer visit patterns for staffing</li>
                  <li>• Implement automated branch follow-up systems</li>
                  <li>• Track timing changes as early churn indicators</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
