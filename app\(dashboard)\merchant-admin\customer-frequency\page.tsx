"use client";

import { useState } from "react";
import {
  Activity,
  Clock,
  Users,
  TrendingUp,
  AlertTriangle,
  Calendar,
  BarChart3
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  Scatter<PERSON>hart,
  <PERSON>att<PERSON>,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for enhanced frequency analysis
const dummyFrequencyDistribution = [
  { range: "Daily (1-2 days)", customers: 45, percentage: 12 },
  { range: "Weekly (3-7 days)", customers: 120, percentage: 32 },
  { range: "Bi-weekly (8-14 days)", customers: 85, percentage: 23 },
  { range: "Monthly (15-30 days)", customers: 78, percentage: 21 },
  { range: "Quarterly (31-90 days)", customers: 32, percentage: 9 },
  { range: "Rarely (90+ days)", customers: 15, percentage: 4 }
];

const dummyFrequencyTrends = [
  { month: "Jan", avgDays: 12.5, activeCustomers: 285, newCustomers: 25 },
  { month: "Feb", avgDays: 11.8, activeCustomers: 310, newCustomers: 35 },
  { month: "Mar", avgDays: 13.2, activeCustomers: 295, newCustomers: 20 },
  { month: "Apr", avgDays: 10.9, activeCustomers: 325, newCustomers: 40 },
  { month: "May", avgDays: 11.5, activeCustomers: 340, newCustomers: 45 },
  { month: "Jun", avgDays: 12.1, activeCustomers: 355, newCustomers: 30 }
];

const dummyCustomerSegments = [
  { segment: "High Frequency", count: 65, avgDays: 3.2, color: COLORS[0] },
  { segment: "Medium Frequency", count: 155, avgDays: 8.5, color: COLORS[1] },
  { segment: "Low Frequency", count: 135, avgDays: 25.8, color: COLORS[2] }
];

const dummyRetentionData = [
  { period: "Week 1", retained: 95, churned: 5 },
  { period: "Week 2", retained: 88, churned: 12 },
  { period: "Week 3", retained: 82, churned: 18 },
  { period: "Month 1", retained: 75, churned: 25 },
  { period: "Month 2", retained: 68, churned: 32 },
  { period: "Month 3", retained: 62, churned: 38 }
];

// Utility functions for data transformation
const transformDaysBetweenTransactionsData = (daysBetweenData: any) => {
  if (!daysBetweenData?.data) return [];

  const customerGroups = daysBetweenData.data.reduce((acc: any, item: any) => {
    if (!acc[item.customer_id]) {
      acc[item.customer_id] = {
        days: [],
        totalTransactions: 0
      };
    }

    acc[item.customer_id].totalTransactions++;

    if (item.days_since !== null && item.days_since !== undefined) {
      acc[item.customer_id].days.push(item.days_since);
    }

    return acc;
  }, {});

  return Object.entries(customerGroups)
    .map(([customerId, data]: [string, any]) => {
      const { days, totalTransactions } = data;

      const avgDays = days.length > 0
        ? Math.round((days.reduce((sum: number, day: number) => sum + day, 0) / days.length) * 10) / 10
        : 0;

      const minDays = days.length > 0 ? Math.min(...days) : 0;
      const maxDays = days.length > 0 ? Math.max(...days) : 0;

      return {
        customerId,
        averageDays: avgDays,
        minDays,
        maxDays,
        totalTransactions,
        validIntervals: days.length,
        frequency: avgDays > 0 ? `Every ${avgDays} days` : 'Single visit'
      };
    })
    .filter(customer => customer.validIntervals > 0)
    .sort((a, b) => a.averageDays - b.averageDays);
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

export default function CustomerFrequencyPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform API data
  const daysBetweenData = overviewData ? transformDaysBetweenTransactionsData(overviewData.days_between_transactions) : [];

  // Calculate KPIs from frequency data
  const avgFrequency = daysBetweenData.length > 0
    ? Math.round(daysBetweenData.reduce((sum, item) => sum + item.averageDays, 0) / daysBetweenData.length * 10) / 10
    : 0;
  const activeCustomers = daysBetweenData.length;
  const highFrequencyCustomers = daysBetweenData.filter(customer => customer.averageDays <= 7).length;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Customer Transaction Frequency</h2>
        <p className="text-muted-foreground">
          Analyze customer transaction patterns and visit frequency behaviors
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load frequency data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No customer frequency data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Average Frequency"
          value={isLoading ? "..." : error ? "0 days" : `${avgFrequency} days`}
          icon={Clock}
          trend={{ value: 8.3, isPositive: false }}
        />
        <KpiCard
          title="Active Customers"
          value={isLoading ? "..." : error ? "0" : activeCustomers.toString()}
          icon={Users}
          trend={{ value: 12.7, isPositive: true }}
        />
        <KpiCard
          title="High Frequency Customers"
          value={isLoading ? "..." : error ? "0" : highFrequencyCustomers.toString()}
          icon={TrendingUp}
          trend={{ value: 15.4, isPositive: true }}
        />
        <KpiCard
          title="Retention Rate"
          value="68%"
          icon={Calendar}
          trend={{ value: 5.2, isPositive: true }}
        />
      </div>

      {/* Frequency Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Transaction Frequency">
          {(() => {
            const state = getChartState(isLoading, error, daysBetweenData.length > 0, "transaction frequency data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={daysBetweenData.slice(0, 10)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="customerId"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      fontSize={12}
                    />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        `${value} days`,
                        name === "averageDays" ? "Avg Days Between Transactions" : name
                      ]}
                    />
                    <Bar dataKey="averageDays" fill={COLORS[0]} name="Avg Days Between Transactions" />
                  </BarChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Frequency Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyFrequencyDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="range"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value, name) => [value, name === "customers" ? "Customers" : "Percentage"]} />
              <Bar dataKey="customers" fill={COLORS[1]} name="Customer Count" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Frequency Trends & Segments */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Frequency Trends Over Time">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummyFrequencyTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "avgDays" ? `${value} days` : value,
                name === "avgDays" ? "Avg Days" : name === "activeCustomers" ? "Active Customers" : "New Customers"
              ]} />
              <Line type="monotone" dataKey="avgDays" stroke={COLORS[2]} strokeWidth={3} name="Avg Days" />
              <Line type="monotone" dataKey="activeCustomers" stroke={COLORS[3]} strokeWidth={3} name="Active Customers" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Customer Frequency Segments">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyCustomerSegments}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ segment, count }) => `${segment}: ${count}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {dummyCustomerSegments.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Customers"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Retention Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Retention Analysis">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={dummyRetentionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip formatter={(value, name) => [`${value}%`, name === "retained" ? "Retained" : "Churned"]} />
              <Area type="monotone" dataKey="retained" stackId="1" stroke={COLORS[4]} fill={COLORS[4]} name="Retained" />
              <Area type="monotone" dataKey="churned" stackId="1" stroke={COLORS[5]} fill={COLORS[5]} name="Churned" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Frequency vs Transaction Volume">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart data={daysBetweenData.slice(0, 20)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="averageDays" name="Avg Days" />
              <YAxis dataKey="totalTransactions" name="Total Transactions" />
              <Tooltip
                formatter={(value, name) => [
                  name === "totalTransactions" ? value : `${value} days`,
                  name === "totalTransactions" ? "Total Transactions" : "Avg Days Between"
                ]}
              />
              <Scatter dataKey="totalTransactions" fill={COLORS[0]} />
            </ScatterChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Frequency Table */}
      <div>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="ml-2">Loading frequency data...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
              {error.message?.includes('No data after filtering')
                ? "No customer frequency data available for the selected time period"
                : `Error loading frequency data: ${error.message}`
              }
            </div>
          </div>
        ) : (
          <DataTable
            title="Customer Transaction Frequency Analysis"
            columns={[
              { key: "customerId", title: "Customer ID" },
              { key: "frequency", title: "Frequency" },
              { key: "totalTransactions", title: "Total Transactions" },
              { key: "validIntervals", title: "Valid Intervals" },
            ]}
            data={daysBetweenData}
          />
        )}
      </div>

      {/* Frequency Insights */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Frequency Segment Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {dummyCustomerSegments.map((segment, index) => (
                <div key={segment.segment} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: segment.color }}
                    ></div>
                    <h3 className="font-semibold">{segment.segment}</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Customers:</span>
                      <span className="font-medium">{segment.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Avg Days:</span>
                      <span className="font-medium">{segment.avgDays}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Frequency:</span>
                      <span className="font-medium">
                        {segment.avgDays <= 7 ? 'Weekly+' :
                         segment.avgDays <= 14 ? 'Bi-weekly' :
                         'Monthly+'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Frequency Recommendations */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Frequency Optimization Strategies</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">High Frequency Customers</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Maintain engagement with loyalty rewards</li>
                  <li>• Offer exclusive deals and early access</li>
                  <li>• Monitor for any decline in visit frequency</li>
                  <li>• Use as brand ambassadors and referral sources</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Medium Frequency Customers</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Implement targeted campaigns to increase visits</li>
                  <li>• Send personalized reminders and offers</li>
                  <li>• Analyze barriers to more frequent visits</li>
                  <li>• Create incentives for shorter visit intervals</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Low Frequency Customers</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Focus on re-engagement campaigns</li>
                  <li>• Understand reasons for infrequent visits</li>
                  <li>• Offer special promotions to encourage return</li>
                  <li>• Consider customer satisfaction surveys</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">Overall Strategy</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Track frequency trends and seasonal patterns</li>
                  <li>• Implement automated engagement workflows</li>
                  <li>• Optimize service delivery and customer experience</li>
                  <li>• Regular analysis of frequency drivers and barriers</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
