"use client";

import { useState } from "react";
import {
  Activity,
  Clock,
  Users,
  TrendingUp,
  AlertTriangle,
  Calendar,
  BarChart3,
  Target
} from "lucide-react";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  ScatterChart,
  Scatter,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend,
  ComposedChart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for enhanced transaction timing analysis
const dummyCustomerBehaviorSegments = [
  { segment: "Daily Visitors (1-2 days)", customers: 45, avgDays: 1.5, retention: 95, color: COLORS[0] },
  { segment: "Weekly Visitors (3-7 days)", customers: 120, avgDays: 5.2, retention: 88, color: COLORS[1] },
  { segment: "Bi-weekly Visitors (8-14 days)", customers: 85, avgDays: 11.3, retention: 75, color: COLORS[2] },
  { segment: "Monthly Visitors (15-30 days)", customers: 78, avgDays: 22.8, retention: 62, color: COLORS[3] },
  { segment: "Quarterly Visitors (31-90 days)", customers: 32, avgDays: 58.5, retention: 45, color: COLORS[4] },
  { segment: "Infrequent Visitors (90+ days)", customers: 15, avgDays: 125.0, retention: 25, color: COLORS[5] }
];

const dummyPurchasingPatterns = [
  { pattern: "Impulse Buyers", avgDays: 2.1, avgAmount: 150, frequency: "High", characteristics: "Quick decisions, frequent small purchases" },
  { pattern: "Planned Purchasers", avgDays: 14.5, avgAmount: 450, frequency: "Medium", characteristics: "Scheduled visits, larger transactions" },
  { pattern: "Seasonal Buyers", avgDays: 45.2, avgAmount: 800, frequency: "Low", characteristics: "Periodic large purchases, event-driven" },
  { pattern: "Loyal Regulars", avgDays: 7.8, avgAmount: 280, frequency: "High", characteristics: "Consistent patterns, predictable timing" }
];

const dummyTimingTrends = [
  { month: "Jan", avgDays: 12.5, newCustomers: 25, returningCustomers: 285, churnRate: 8.2 },
  { month: "Feb", avgDays: 11.8, newCustomers: 35, returningCustomers: 310, churnRate: 7.8 },
  { month: "Mar", avgDays: 13.2, newCustomers: 20, returningCustomers: 295, churnRate: 9.1 },
  { month: "Apr", avgDays: 10.9, newCustomers: 40, returningCustomers: 325, churnRate: 6.5 },
  { month: "May", avgDays: 11.5, newCustomers: 45, returningCustomers: 340, churnRate: 7.2 },
  { month: "Jun", avgDays: 12.1, newCustomers: 30, returningCustomers: 355, churnRate: 8.0 }
];

const dummyRetentionAnalysis = [
  { daysRange: "1-7 days", customers: 165, retentionRate: 92, avgLifetime: 18, revenue: 285000 },
  { daysRange: "8-14 days", customers: 85, retentionRate: 78, avgLifetime: 14, revenue: 195000 },
  { daysRange: "15-30 days", customers: 78, retentionRate: 65, avgLifetime: 12, revenue: 165000 },
  { daysRange: "31-60 days", customers: 45, retentionRate: 52, avgLifetime: 8, revenue: 125000 },
  { daysRange: "60+ days", customers: 32, retentionRate: 35, avgLifetime: 6, revenue: 85000 }
];

// Utility functions for data transformation
const transformDaysBetweenTransactionsData = (daysBetweenData: any) => {
  if (!daysBetweenData?.data) return [];

  const customerGroups = daysBetweenData.data.reduce((acc: any, item: any) => {
    if (!acc[item.customer_id]) {
      acc[item.customer_id] = {
        days: [],
        totalTransactions: 0
      };
    }

    acc[item.customer_id].totalTransactions++;

    if (item.days_since !== null && item.days_since !== undefined) {
      acc[item.customer_id].days.push(item.days_since);
    }

    return acc;
  }, {});

  return Object.entries(customerGroups)
    .map(([customerId, data]: [string, any]) => {
      const { days, totalTransactions } = data;

      const avgDays = days.length > 0
        ? Math.round((days.reduce((sum: number, day: number) => sum + day, 0) / days.length) * 10) / 10
        : 0;

      const minDays = days.length > 0 ? Math.min(...days) : 0;
      const maxDays = days.length > 0 ? Math.max(...days) : 0;

      return {
        customerId,
        averageDays: avgDays,
        minDays,
        maxDays,
        totalTransactions,
        validIntervals: days.length,
        frequency: avgDays > 0 ? `Every ${avgDays} days` : 'Single visit',
        behaviorType: avgDays <= 7 ? 'Frequent' : avgDays <= 30 ? 'Regular' : 'Infrequent'
      };
    })
    .filter(customer => customer.validIntervals > 0)
    .sort((a, b) => a.averageDays - b.averageDays);
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

export default function TransactionTimingPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform API data
  const daysBetweenData = overviewData ? transformDaysBetweenTransactionsData(overviewData.days_between_transactions) : [];

  // Calculate KPIs from timing data
  const avgTimeBetween = daysBetweenData.length > 0
    ? Math.round(daysBetweenData.reduce((sum, item) => sum + item.averageDays, 0) / daysBetweenData.length * 10) / 10
    : 0;
  const returningCustomers = daysBetweenData.length;
  const frequentCustomers = daysBetweenData.filter(customer => customer.averageDays <= 7).length;
  const avgRetentionRate = 72; // Calculated from dummy data

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Time Between Transactions Analysis</h2>
        <p className="text-muted-foreground">
          Analyze the average time between transactions for returning customers to understand purchasing behavior
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load transaction timing data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No transaction timing data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Avg Time Between Transactions"
          value={isLoading ? "..." : error ? "0 days" : `${avgTimeBetween} days`}
          icon={Clock}
          trend={{ value: 8.3, isPositive: false }}
        />
        <KpiCard
          title="Returning Customers"
          value={isLoading ? "..." : error ? "0" : returningCustomers.toString()}
          icon={Users}
          trend={{ value: 12.7, isPositive: true }}
        />
        <KpiCard
          title="Frequent Customers"
          value={isLoading ? "..." : error ? "0" : frequentCustomers.toString()}
          icon={TrendingUp}
          trend={{ value: 15.4, isPositive: true }}
        />
        <KpiCard
          title="Avg Retention Rate"
          value={`${avgRetentionRate}%`}
          icon={Target}
          trend={{ value: 5.2, isPositive: true }}
        />
      </div>

      {/* Transaction Timing Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Transaction Intervals">
          {(() => {
            const state = getChartState(isLoading, error, daysBetweenData.length > 0, "transaction timing data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={daysBetweenData.slice(0, 15)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="customerId"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      fontSize={12}
                    />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        `${value} days`,
                        name === "averageDays" ? "Avg Days Between Transactions" : name
                      ]}
                    />
                    <Bar dataKey="averageDays" fill={COLORS[0]} name="Avg Days Between Transactions" />
                  </BarChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Customer Behavior Segments">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyCustomerBehaviorSegments}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ segment, customers }) => `${customers}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="customers"
              >
                {dummyCustomerBehaviorSegments.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Customers"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Timing Trends & Retention Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Transaction Timing Trends">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={dummyTimingTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "avgDays" ? `${value} days` :
                name === "churnRate" ? `${value}%` : value,
                name === "avgDays" ? "Avg Days" :
                name === "churnRate" ? "Churn Rate" :
                name === "newCustomers" ? "New Customers" : "Returning Customers"
              ]} />
              <Bar dataKey="returningCustomers" fill={COLORS[1]} name="Returning Customers" />
              <Line type="monotone" dataKey="avgDays" stroke={COLORS[0]} strokeWidth={3} name="Avg Days" />
              <Line type="monotone" dataKey="churnRate" stroke={COLORS[2]} strokeWidth={3} name="Churn Rate %" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Retention by Transaction Frequency">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={dummyRetentionAnalysis}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="daysRange" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "retentionRate" ? `${value}%` :
                name === "revenue" ? formatCurrency(Number(value)) : value,
                name === "retentionRate" ? "Retention Rate" :
                name === "revenue" ? "Revenue" :
                name === "customers" ? "Customers" : "Avg Lifetime"
              ]} />
              <Area type="monotone" dataKey="retentionRate" stroke={COLORS[3]} fill={COLORS[3]} fillOpacity={0.3} name="Retention Rate %" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Purchasing Patterns & Correlation Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Transaction Frequency vs Amount">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart data={daysBetweenData.slice(0, 20)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="averageDays" name="Avg Days" />
              <YAxis dataKey="totalTransactions" name="Total Transactions" />
              <Tooltip
                formatter={(value, name) => [
                  name === "totalTransactions" ? value : `${value} days`,
                  name === "totalTransactions" ? "Total Transactions" : "Avg Days Between"
                ]}
              />
              <Scatter dataKey="totalTransactions" fill={COLORS[4]} />
            </ScatterChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Customer Lifetime Value by Frequency">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyRetentionAnalysis}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="daysRange"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "revenue" ? formatCurrency(Number(value)) : value,
                name === "revenue" ? "Revenue" : "Avg Lifetime (months)"
              ]} />
              <Bar dataKey="revenue" fill={COLORS[5]} name="Revenue" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Transaction Timing Table */}
      <div>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="ml-2">Loading timing data...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
              {error.message?.includes('No data after filtering')
                ? "No transaction timing data available for the selected time period"
                : `Error loading timing data: ${error.message}`
              }
            </div>
          </div>
        ) : (
          <DataTable
            title="Customer Transaction Timing Analysis"
            columns={[
              { key: "customerId", title: "Customer ID" },
              { key: "frequency", title: "Frequency" },
              { key: "totalTransactions", title: "Total Transactions" },
              { key: "behaviorType", title: "Behavior Type" },
            ]}
            data={daysBetweenData}
          />
        )}
      </div>

      {/* Purchasing Patterns Analysis */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Customer Purchasing Patterns</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Pattern Type</th>
                    <th className="text-left p-2">Avg Days Between</th>
                    <th className="text-left p-2">Avg Amount</th>
                    <th className="text-left p-2">Frequency</th>
                    <th className="text-left p-2">Characteristics</th>
                  </tr>
                </thead>
                <tbody>
                  {dummyPurchasingPatterns.map((pattern, index) => (
                    <tr key={pattern.pattern} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{pattern.pattern}</td>
                      <td className="p-2">{pattern.avgDays} days</td>
                      <td className="p-2">{formatCurrency(pattern.avgAmount)}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          pattern.frequency === 'High' ? 'bg-green-100 text-green-800' :
                          pattern.frequency === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {pattern.frequency}
                        </span>
                      </td>
                      <td className="p-2 text-sm text-muted-foreground">{pattern.characteristics}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Behavior Insights */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Transaction Timing Insights & Strategies</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Frequent Customers (1-7 days)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• High engagement and loyalty potential</li>
                  <li>• Focus on maintaining consistent service quality</li>
                  <li>• Offer loyalty rewards and exclusive benefits</li>
                  <li>• Monitor for any changes in visit patterns</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Regular Customers (8-30 days)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Stable customer base with predictable patterns</li>
                  <li>• Target for frequency increase initiatives</li>
                  <li>• Send timely reminders and personalized offers</li>
                  <li>• Analyze barriers to more frequent visits</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Infrequent Customers (30+ days)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• High risk of churn, require re-engagement</li>
                  <li>• Implement win-back campaigns and special offers</li>
                  <li>• Understand reasons for infrequent visits</li>
                  <li>• Consider customer satisfaction surveys</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">Optimization Strategies</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Use timing data for personalized marketing</li>
                  <li>• Predict customer visit patterns and prepare accordingly</li>
                  <li>• Implement automated follow-up systems</li>
                  <li>• Track timing changes as early churn indicators</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
