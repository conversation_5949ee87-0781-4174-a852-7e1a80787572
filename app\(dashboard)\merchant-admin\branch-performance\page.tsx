"use client";

import { useState } from "react";
import {
  Building,
  TrendingUp,
  Users,
  DollarSign,
  AlertTriangle,
  Activity,
  Award,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  PieChart,
  Pie,
  Cell,
  Legend,
  ComposedChart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for branch performance analysis
const dummyBranchPerformance = [
  {
    branchId: "BR-001",
    branchName: "Main Branch",
    revenue: 125000,
    transactions: 1250,
    customers: 450,
    tellers: 8,
    avgTransactionValue: 100,
    efficiency: 92,
    customerSatisfaction: 4.5,
    rank: 1
  },
  {
    branchId: "BR-002",
    branchName: "Downtown Branch",
    revenue: 98000,
    transactions: 1100,
    customers: 380,
    tellers: 6,
    avgTransactionValue: 89,
    efficiency: 88,
    customerSatisfaction: 4.3,
    rank: 2
  },
  {
    branchId: "BR-003",
    branchName: "Suburban Branch",
    revenue: 87000,
    transactions: 950,
    customers: 320,
    tellers: 5,
    avgTransactionValue: 92,
    efficiency: 85,
    customerSatisfaction: 4.2,
    rank: 3
  },
  {
    branchId: "BR-004",
    branchName: "Mall Branch",
    revenue: 76000,
    transactions: 850,
    customers: 290,
    tellers: 4,
    avgTransactionValue: 89,
    efficiency: 82,
    customerSatisfaction: 4.0,
    rank: 4
  },
  {
    branchId: "BR-005",
    branchName: "Airport Branch",
    revenue: 65000,
    transactions: 720,
    customers: 250,
    tellers: 4,
    avgTransactionValue: 90,
    efficiency: 78,
    customerSatisfaction: 3.8,
    rank: 5
  }
];

const dummyBranchTrends = [
  { month: "Jan", mainBranch: 115000, downtown: 92000, suburban: 78000, mall: 68000, airport: 58000 },
  { month: "Feb", mainBranch: 118000, downtown: 95000, suburban: 82000, mall: 71000, airport: 61000 },
  { month: "Mar", mainBranch: 122000, downtown: 96000, suburban: 85000, mall: 74000, airport: 63000 },
  { month: "Apr", mainBranch: 125000, downtown: 98000, suburban: 87000, mall: 76000, airport: 65000 },
  { month: "May", mainBranch: 128000, downtown: 101000, suburban: 89000, mall: 78000, airport: 67000 },
  { month: "Jun", mainBranch: 125000, downtown: 98000, suburban: 87000, mall: 76000, airport: 65000 }
];

const dummyBranchMetrics = [
  { metric: "Revenue", mainBranch: 95, downtown: 85, suburban: 78, mall: 70, airport: 62 },
  { metric: "Efficiency", mainBranch: 92, downtown: 88, suburban: 85, mall: 82, airport: 78 },
  { metric: "Customer Satisfaction", mainBranch: 90, downtown: 86, suburban: 84, mall: 80, airport: 76 },
  { metric: "Transaction Volume", mainBranch: 88, downtown: 82, suburban: 76, mall: 72, airport: 68 },
  { metric: "Staff Performance", mainBranch: 94, downtown: 89, suburban: 86, mall: 83, airport: 79 }
];

const dummyBranchComparison = [
  { category: "High Performers", count: 2, percentage: 40, color: COLORS[0] },
  { category: "Good Performers", count: 2, percentage: 40, color: COLORS[1] },
  { category: "Needs Improvement", count: 1, percentage: 20, color: COLORS[2] }
];

const dummyTellerPerformance = [
  { branchId: "BR-001", tellerCount: 8, avgPerformance: 92, topPerformer: "T-001", efficiency: 95 },
  { branchId: "BR-002", tellerCount: 6, avgPerformance: 88, topPerformer: "T-007", efficiency: 91 },
  { branchId: "BR-003", tellerCount: 5, avgPerformance: 85, topPerformer: "T-012", efficiency: 88 },
  { branchId: "BR-004", tellerCount: 4, avgPerformance: 82, topPerformer: "T-016", efficiency: 85 },
  { branchId: "BR-005", tellerCount: 4, avgPerformance: 78, topPerformer: "T-019", efficiency: 82 }
];

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

export default function BranchPerformancePage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Calculate KPIs from branch data
  const totalBranches = dummyBranchPerformance.length;
  const totalRevenue = dummyBranchPerformance.reduce((sum, branch) => sum + branch.revenue, 0);
  const avgEfficiency = Math.round(dummyBranchPerformance.reduce((sum, branch) => sum + branch.efficiency, 0) / totalBranches);
  const topPerformingBranches = dummyBranchPerformance.filter(branch => branch.efficiency >= 90).length;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Branch Performance Analysis</h2>
        <p className="text-muted-foreground">
          Comprehensive analysis of branch performance, efficiency, and comparative metrics
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={false}
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Branches"
          value={totalBranches.toString()}
          icon={Building}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="Total Revenue"
          value={formatCurrency(totalRevenue)}
          icon={DollarSign}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Average Efficiency"
          value={`${avgEfficiency}%`}
          icon={Target}
          trend={{ value: 8.3, isPositive: true }}
        />
        <KpiCard
          title="Top Performers"
          value={topPerformingBranches.toString()}
          icon={Award}
          trend={{ value: 15.7, isPositive: true }}
        />
      </div>

      {/* Branch Performance Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Revenue Comparison">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchPerformance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="branchName"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Revenue"]} />
              <Bar dataKey="revenue" fill={COLORS[0]} name="Revenue" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Efficiency Scores">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchPerformance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="branchName"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis domain={[0, 100]} />
              <Tooltip formatter={(value) => [`${value}%`, "Efficiency"]} />
              <Bar dataKey="efficiency" fill={COLORS[1]} name="Efficiency %" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Branch Trends & Performance Matrix */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Revenue Trends">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummyBranchTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Revenue"]} />
              <Line type="monotone" dataKey="mainBranch" stroke={COLORS[0]} strokeWidth={2} name="Main Branch" />
              <Line type="monotone" dataKey="downtown" stroke={COLORS[1]} strokeWidth={2} name="Downtown" />
              <Line type="monotone" dataKey="suburban" stroke={COLORS[2]} strokeWidth={2} name="Suburban" />
              <Line type="monotone" dataKey="mall" stroke={COLORS[3]} strokeWidth={2} name="Mall" />
              <Line type="monotone" dataKey="airport" stroke={COLORS[4]} strokeWidth={2} name="Airport" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Performance Matrix">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={dummyBranchMetrics}>
              <PolarGrid />
              <PolarAngleAxis dataKey="metric" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar name="Main Branch" dataKey="mainBranch" stroke={COLORS[0]} fill={COLORS[0]} fillOpacity={0.1} />
              <Radar name="Downtown" dataKey="downtown" stroke={COLORS[1]} fill={COLORS[1]} fillOpacity={0.1} />
              <Radar name="Suburban" dataKey="suburban" stroke={COLORS[2]} fill={COLORS[2]} fillOpacity={0.1} />
              <Tooltip />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Performance Distribution & Teller Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Performance Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyBranchComparison}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ category, percentage }) => `${category}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {dummyBranchComparison.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Branches"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Teller Performance by Branch">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={dummyTellerPerformance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="branchId" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "tellerCount" ? value : `${value}%`,
                name === "tellerCount" ? "Teller Count" : name === "avgPerformance" ? "Avg Performance" : "Efficiency"
              ]} />
              <Bar dataKey="tellerCount" fill={COLORS[3]} name="Teller Count" />
              <Line type="monotone" dataKey="avgPerformance" stroke={COLORS[4]} strokeWidth={3} name="Avg Performance" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Branch Performance Table */}
      <div>
        <DataTable
          title="Detailed Branch Performance"
          columns={[
            { key: "rank", title: "Rank" },
            { key: "branchName", title: "Branch Name" },
            { key: "revenue", title: "Revenue", render: (value) => formatCurrency(value) },
            { key: "transactions", title: "Transactions" },
            { key: "customers", title: "Customers" },
            { key: "tellers", title: "Tellers" },
            { key: "efficiency", title: "Efficiency", render: (value) => `${value}%` },
            { key: "customerSatisfaction", title: "Satisfaction", render: (value) => `${value}/5` },
          ]}
          data={dummyBranchPerformance}
        />
      </div>

      {/* Branch Analysis Summary */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <span>Branch Performance Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {dummyBranchPerformance.slice(0, 3).map((branch, index) => (
                <div key={branch.branchId} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <div
                      className={`w-4 h-4 rounded-full ${
                        index === 0 ? 'bg-green-500' :
                        index === 1 ? 'bg-blue-500' :
                        'bg-orange-500'
                      }`}
                    ></div>
                    <h3 className="font-semibold">{branch.branchName}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      index === 0 ? 'bg-green-100 text-green-800' :
                      index === 1 ? 'bg-blue-100 text-blue-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      #{branch.rank}
                    </span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Revenue:</span>
                      <span className="font-medium">{formatCurrency(branch.revenue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Efficiency:</span>
                      <span className="font-medium">{branch.efficiency}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Customers:</span>
                      <span className="font-medium">{branch.customers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Satisfaction:</span>
                      <span className="font-medium">{branch.customerSatisfaction}/5</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Branch Improvement Recommendations */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Branch Performance Optimization</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Top Performing Branches</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Maintain current service standards and efficiency</li>
                  <li>• Share best practices with other branches</li>
                  <li>• Consider expansion or additional services</li>
                  <li>• Use as training centers for new staff</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Good Performing Branches</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Implement targeted improvement initiatives</li>
                  <li>• Focus on customer satisfaction enhancement</li>
                  <li>• Optimize staff allocation and training</li>
                  <li>• Monitor progress with regular assessments</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Improvement Needed</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Conduct comprehensive performance review</li>
                  <li>• Implement intensive staff training programs</li>
                  <li>• Review operational processes and procedures</li>
                  <li>• Consider management changes if necessary</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">Overall Strategy</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Regular performance monitoring and benchmarking</li>
                  <li>• Cross-branch knowledge sharing sessions</li>
                  <li>• Standardize best practices across all branches</li>
                  <li>• Implement performance-based incentive programs</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
