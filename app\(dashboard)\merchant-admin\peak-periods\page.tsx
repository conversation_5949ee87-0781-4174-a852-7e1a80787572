"use client";

import { useState } from "react";
import {
  Bar<PERSON>hart3,
  Clock,
  Calendar,
  TrendingUp,
  AlertTriangle,
  Activity,
  Users,
  Target
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  ComposedChart,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for peak transaction periods analysis
const dummyHourlyPatterns = [
  { hour: "00:00", mainBranch: 15, downtown: 12, suburban: 8, mall: 5, airport: 18 },
  { hour: "01:00", mainBranch: 8, downtown: 6, suburban: 4, mall: 2, airport: 12 },
  { hour: "02:00", mainBranch: 5, downtown: 4, suburban: 2, mall: 1, airport: 8 },
  { hour: "03:00", mainBranch: 3, downtown: 2, suburban: 1, mall: 0, airport: 5 },
  { hour: "04:00", mainBranch: 2, downtown: 1, suburban: 1, mall: 0, airport: 3 },
  { hour: "05:00", mainBranch: 8, downtown: 5, suburban: 3, mall: 1, airport: 15 },
  { hour: "06:00", mainBranch: 25, downtown: 18, suburban: 12, mall: 5, airport: 35 },
  { hour: "07:00", mainBranch: 45, downtown: 35, suburban: 25, mall: 12, airport: 55 },
  { hour: "08:00", mainBranch: 85, downtown: 65, suburban: 45, mall: 25, airport: 75 },
  { hour: "09:00", mainBranch: 120, downtown: 95, suburban: 65, mall: 35, airport: 85 },
  { hour: "10:00", mainBranch: 145, downtown: 115, suburban: 85, mall: 55, airport: 95 },
  { hour: "11:00", mainBranch: 165, downtown: 135, suburban: 95, mall: 65, airport: 105 },
  { hour: "12:00", mainBranch: 185, downtown: 155, suburban: 115, mall: 85, airport: 125 },
  { hour: "13:00", mainBranch: 175, downtown: 145, suburban: 105, mall: 75, airport: 115 },
  { hour: "14:00", mainBranch: 155, downtown: 125, suburban: 95, mall: 65, airport: 105 },
  { hour: "15:00", mainBranch: 135, downtown: 105, suburban: 75, mall: 55, airport: 95 },
  { hour: "16:00", mainBranch: 125, downtown: 95, suburban: 65, mall: 45, airport: 85 },
  { hour: "17:00", mainBranch: 105, downtown: 85, suburban: 55, mall: 35, airport: 75 },
  { hour: "18:00", mainBranch: 85, downtown: 65, suburban: 45, mall: 25, airport: 65 },
  { hour: "19:00", mainBranch: 65, downtown: 45, suburban: 35, mall: 15, airport: 45 },
  { hour: "20:00", mainBranch: 45, downtown: 35, suburban: 25, mall: 12, airport: 35 },
  { hour: "21:00", mainBranch: 35, downtown: 25, suburban: 18, mall: 8, airport: 25 },
  { hour: "22:00", mainBranch: 25, downtown: 18, suburban: 12, mall: 5, airport: 20 },
  { hour: "23:00", mainBranch: 18, downtown: 12, suburban: 8, mall: 3, airport: 15 }
];

const dummyDailyPatterns = [
  { day: "Monday", transactions: 1250, volume: 125000, avgValue: 100, peakHour: "12:00" },
  { day: "Tuesday", transactions: 1380, volume: 138000, avgValue: 100, peakHour: "11:00" },
  { day: "Wednesday", transactions: 1320, volume: 132000, avgValue: 100, peakHour: "12:00" },
  { day: "Thursday", transactions: 1450, volume: 145000, avgValue: 100, peakHour: "13:00" },
  { day: "Friday", transactions: 1680, volume: 168000, avgValue: 100, peakHour: "12:00" },
  { day: "Saturday", transactions: 980, volume: 98000, avgValue: 100, peakHour: "14:00" },
  { day: "Sunday", transactions: 650, volume: 65000, avgValue: 100, peakHour: "15:00" }
];

const dummyBranchPeakHours = [
  { branchName: "Main Branch", peakHour: "12:00-13:00", peakTransactions: 185, peakVolume: 18500, efficiency: 92 },
  { branchName: "Downtown Branch", peakHour: "12:00-13:00", peakTransactions: 155, peakVolume: 15500, efficiency: 88 },
  { branchName: "Suburban Branch", peakHour: "11:00-12:00", peakTransactions: 115, peakVolume: 11500, efficiency: 85 },
  { branchName: "Mall Branch", peakHour: "14:00-15:00", peakTransactions: 85, peakVolume: 8500, efficiency: 82 },
  { branchName: "Airport Branch", peakHour: "07:00-08:00", peakTransactions: 125, peakVolume: 12500, efficiency: 78 }
];

const dummySeasonalTrends = [
  { month: "Jan", weekday: 1200, weekend: 800, holiday: 450 },
  { month: "Feb", weekday: 1250, weekend: 850, holiday: 500 },
  { month: "Mar", weekday: 1300, weekend: 900, holiday: 550 },
  { month: "Apr", weekday: 1350, weekend: 950, holiday: 600 },
  { month: "May", weekday: 1400, weekend: 1000, holiday: 650 },
  { month: "Jun", weekday: 1450, weekend: 1050, holiday: 700 }
];

const dummyPeakPeriodTypes = [
  { type: "Morning Rush (8-10 AM)", percentage: 25, transactions: 3500, color: COLORS[0] },
  { type: "Lunch Peak (12-2 PM)", percentage: 35, transactions: 4900, color: COLORS[1] },
  { type: "Evening Rush (5-7 PM)", percentage: 20, transactions: 2800, color: COLORS[2] },
  { type: "Off-Peak Hours", percentage: 20, transactions: 2800, color: COLORS[3] }
];

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

export default function PeakPeriodsPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Calculate KPIs from peak periods data
  const totalBranches = dummyBranchPeakHours.length;
  const avgPeakTransactions = Math.round(dummyBranchPeakHours.reduce((sum, branch) => sum + branch.peakTransactions, 0) / totalBranches);
  const totalPeakVolume = dummyBranchPeakHours.reduce((sum, branch) => sum + branch.peakVolume, 0);
  const mostCommonPeakHour = "12:00-13:00"; // Most frequent peak hour

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Peak Transaction Periods</h2>
        <p className="text-muted-foreground">
          Identify peak hours and days per branch to optimize staffing and resource allocation
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={false}
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Branches"
          value={totalBranches.toString()}
          icon={Users}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="Avg Peak Transactions"
          value={avgPeakTransactions.toString()}
          icon={BarChart3}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Peak Volume"
          value={formatCurrency(totalPeakVolume)}
          icon={TrendingUp}
          trend={{ value: 18.3, isPositive: true }}
        />
        <KpiCard
          title="Common Peak Hour"
          value={mostCommonPeakHour}
          icon={Clock}
          trend={{ value: 8.7, isPositive: true }}
        />
      </div>

      {/* Peak Period Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Hourly Transaction Patterns by Branch">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummyHourlyPatterns}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="hour"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="mainBranch" stroke={COLORS[0]} strokeWidth={2} name="Main Branch" />
              <Line type="monotone" dataKey="downtown" stroke={COLORS[1]} strokeWidth={2} name="Downtown" />
              <Line type="monotone" dataKey="suburban" stroke={COLORS[2]} strokeWidth={2} name="Suburban" />
              <Line type="monotone" dataKey="mall" stroke={COLORS[3]} strokeWidth={2} name="Mall" />
              <Line type="monotone" dataKey="airport" stroke={COLORS[4]} strokeWidth={2} name="Airport" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Daily Transaction Volume Patterns">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={dummyDailyPatterns}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "volume" ? formatCurrency(Number(value)) : value,
                name === "volume" ? "Volume" : name === "transactions" ? "Transactions" : "Avg Value"
              ]} />
              <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
              <Line type="monotone" dataKey="volume" stroke={COLORS[1]} strokeWidth={3} name="Volume" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Peak Hour Analysis & Seasonal Trends */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Peak Hours Comparison">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchPeakHours}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="branchName"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip />
              <Bar dataKey="peakTransactions" fill={COLORS[2]} name="Peak Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Seasonal Transaction Trends">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={dummySeasonalTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="weekday" stackId="1" stroke={COLORS[3]} fill={COLORS[3]} name="Weekday" />
              <Area type="monotone" dataKey="weekend" stackId="1" stroke={COLORS[4]} fill={COLORS[4]} name="Weekend" />
              <Area type="monotone" dataKey="holiday" stackId="1" stroke={COLORS[5]} fill={COLORS[5]} name="Holiday" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Peak Period Distribution & Overall Patterns */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Peak Period Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyPeakPeriodTypes}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ type, percentage }) => `${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="transactions"
              >
                {dummyPeakPeriodTypes.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Transactions"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Combined Hourly Pattern (All Branches)">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={dummyHourlyPatterns.map(item => ({
              hour: item.hour,
              total: item.mainBranch + item.downtown + item.suburban + item.mall + item.airport
            }))}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="hour"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value) => [value, "Total Transactions"]} />
              <Area type="monotone" dataKey="total" stroke={COLORS[0]} fill={COLORS[0]} fillOpacity={0.3} name="Total Transactions" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Branch Peak Hours Table */}
      <div>
        <DataTable
          title="Branch Peak Hours Analysis"
          columns={[
            { key: "branchName", title: "Branch Name" },
            { key: "peakHour", title: "Peak Hour" },
            { key: "peakTransactions", title: "Peak Transactions" },
            { key: "peakVolume", title: "Peak Volume", render: (value) => formatCurrency(value) },
            { key: "efficiency", title: "Efficiency", render: (value) => `${value}%` },
          ]}
          data={dummyBranchPeakHours}
        />
      </div>

      {/* Peak Period Insights */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Peak Period Analysis Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {dummyBranchPeakHours.slice(0, 3).map((branch, index) => (
                <div key={branch.branchName} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <div
                      className={`w-4 h-4 rounded-full ${
                        index === 0 ? 'bg-green-500' :
                        index === 1 ? 'bg-blue-500' :
                        'bg-orange-500'
                      }`}
                    ></div>
                    <h3 className="font-semibold">{branch.branchName}</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Peak Hour:</span>
                      <span className="font-medium">{branch.peakHour}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Peak Transactions:</span>
                      <span className="font-medium">{branch.peakTransactions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Peak Volume:</span>
                      <span className="font-medium">{formatCurrency(branch.peakVolume)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Efficiency:</span>
                      <span className="font-medium">{branch.efficiency}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Peak Period Optimization Recommendations */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Peak Period Optimization Strategies</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Staffing Optimization</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Schedule additional tellers during 12:00-13:00 peak hours</li>
                  <li>• Implement flexible shift patterns based on branch-specific peaks</li>
                  <li>• Cross-train staff to handle multiple service types during rush</li>
                  <li>• Consider part-time staff for peak hour coverage</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Service Efficiency</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Implement express lanes for simple transactions</li>
                  <li>• Promote digital banking during peak hours</li>
                  <li>• Pre-position cash and supplies before peak periods</li>
                  <li>• Use queue management systems to reduce wait times</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Customer Experience</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Provide real-time wait time information</li>
                  <li>• Offer appointment scheduling for complex transactions</li>
                  <li>• Create comfortable waiting areas with amenities</li>
                  <li>• Send peak hour alerts to encourage off-peak visits</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">Operational Planning</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Monitor peak patterns for seasonal adjustments</li>
                  <li>• Plan maintenance and system updates during off-peak hours</li>
                  <li>• Adjust security and cash management for peak periods</li>
                  <li>• Regular review and optimization of peak hour strategies</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
