"use client";

import { useState } from "react";
import {
  <PERSON>ert<PERSON>riangle,
  TrendingUp,
  Activity,
  Shield,
  Eye,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for enhanced outlier analysis
const dummyOutlierTrends = [
  { month: "Jan", outliers: 12, totalTransactions: 1250, percentage: 0.96 },
  { month: "Feb", outliers: 15, totalTransactions: 1380, percentage: 1.09 },
  { month: "Mar", outliers: 8, totalTransactions: 1200, percentage: 0.67 },
  { month: "Apr", outliers: 18, totalTransactions: 1450, percentage: 1.24 },
  { month: "May", outliers: 22, totalTransactions: 1520, percentage: 1.45 },
  { month: "Jun", outliers: 14, totalTransactions: 1350, percentage: 1.04 }
];

const dummyOutlierTypes = [
  { type: "High Value", count: 45, percentage: 65, color: COLORS[0] },
  { type: "Low Value", count: 18, percentage: 26, color: COLORS[1] },
  { type: "Frequency", count: 6, percentage: 9, color: COLORS[2] }
];

const dummyRiskLevels = [
  { level: "Critical", count: 8, threshold: ">₵5000", action: "Immediate Review" },
  { level: "High", count: 15, threshold: "₵2000-₵5000", action: "Priority Investigation" },
  { level: "Medium", count: 25, threshold: "₵500-₵2000", action: "Standard Review" },
  { level: "Low", count: 21, threshold: "<₵500", action: "Monitor" }
];

const dummyOutlierDetails = [
  {
    id: "OUT-001",
    customerId: "CUST-789",
    amount: 8500,
    normalRange: "₵50-₵200",
    deviation: 4250,
    riskLevel: "Critical",
    timestamp: "2024-01-15 14:30",
    status: "Under Review"
  },
  {
    id: "OUT-002",
    customerId: "CUST-456",
    amount: 3200,
    normalRange: "₵100-₵300",
    deviation: 1600,
    riskLevel: "High",
    timestamp: "2024-01-15 11:45",
    status: "Investigated"
  },
  {
    id: "OUT-003",
    customerId: "CUST-123",
    amount: 1800,
    normalRange: "₵200-₵400",
    deviation: 900,
    riskLevel: "Medium",
    timestamp: "2024-01-15 09:20",
    status: "Cleared"
  },
  {
    id: "OUT-004",
    customerId: "CUST-321",
    amount: 25,
    normalRange: "₵200-₵500",
    deviation: -175,
    riskLevel: "Low",
    timestamp: "2024-01-15 16:15",
    status: "Monitoring"
  }
];

// Utility functions for data transformation
const transformOutliersData = (outliers: any) => {
  if (!outliers?.data) return [];

  return outliers.data
    .filter((item: any) => item.outlier)
    .map((outlier: any, index: number) => ({
      rank: index + 1,
      merchant_id: outlier.merchant_id,
      customer_id: outlier.customer_id,
      amount: outlier.amount,
      formattedAmount: formatCurrency(outlier.amount),
      isOutlier: outlier.outlier,
    }));
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

export default function OutlierDetectionPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform API data
  const outliersData = overviewData ? transformOutliersData(overviewData.transaction_outliers) : [];

  // Calculate KPIs from outliers data
  const totalOutliers = outliersData.length;
  const criticalOutliers = dummyRiskLevels.find(level => level.level === "Critical")?.count || 0;
  const highRiskOutliers = dummyRiskLevels.find(level => level.level === "High")?.count || 0;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Outlier Detection & Analysis</h2>
        <p className="text-muted-foreground">
          Monitor and analyze unusual transaction patterns and potential risks
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load outlier data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No outlier data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Outliers"
          value={isLoading ? "..." : error ? "0" : totalOutliers.toString()}
          icon={AlertTriangle}
          trend={{ value: 15.3, isPositive: false }}
        />
        <KpiCard
          title="Critical Risk"
          value={criticalOutliers.toString()}
          icon={Shield}
          trend={{ value: 8.7, isPositive: false }}
        />
        <KpiCard
          title="High Risk"
          value={highRiskOutliers.toString()}
          icon={Eye}
          trend={{ value: 12.4, isPositive: false }}
        />
        <KpiCard
          title="Detection Rate"
          value="1.2%"
          icon={Target}
          trend={{ value: 3.2, isPositive: true }}
        />
      </div>

      {/* Outlier Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Outlier Trends Over Time">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummyOutlierTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "percentage" ? `${value}%` : value,
                name === "outliers" ? "Outliers" : name === "percentage" ? "Detection Rate" : "Total Transactions"
              ]} />
              <Line type="monotone" dataKey="outliers" stroke={COLORS[0]} strokeWidth={3} name="Outliers" />
              <Line type="monotone" dataKey="percentage" stroke={COLORS[1]} strokeWidth={3} name="Detection Rate %" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Outlier Type Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyOutlierTypes}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ type, percentage }) => `${type}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {dummyOutlierTypes.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Count"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Risk Level Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Risk Level Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyRiskLevels}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="level" />
              <YAxis />
              <Tooltip formatter={(value) => [value, "Count"]} />
              <Bar dataKey="count" fill={COLORS[2]} name="Outlier Count" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Risk Assessment Matrix">
          <div className="p-6">
            <div className="grid gap-4">
              {dummyRiskLevels.map((risk, index) => (
                <div key={risk.level} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div
                      className={`w-4 h-4 rounded-full ${
                        risk.level === 'Critical' ? 'bg-red-500' :
                        risk.level === 'High' ? 'bg-orange-500' :
                        risk.level === 'Medium' ? 'bg-yellow-500' :
                        'bg-green-500'
                      }`}
                    ></div>
                    <div>
                      <div className="font-semibold">{risk.level} Risk</div>
                      <div className="text-sm text-muted-foreground">{risk.threshold}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{risk.count}</div>
                    <div className="text-sm text-muted-foreground">{risk.action}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </ChartContainer>
      </div>

      {/* Outliers Table */}
      <div>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="ml-2">Loading outlier data...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
              {error.message?.includes('No data after filtering')
                ? "No transaction outliers detected for the selected time period"
                : `Error loading outlier data: ${error.message}`
              }
            </div>
          </div>
        ) : outliersData.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No transaction outliers detected in the selected period.
          </div>
        ) : (
          <DataTable
            title="Transaction Outliers Analysis"
            columns={[
              { key: "rank", title: "Rank" },
              { key: "customer_id", title: "Customer ID" },
              { key: "formattedAmount", title: "Transaction Amount" },
            ]}
            data={outliersData}
          />
        )}
      </div>

      {/* Detailed Outlier Analysis */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>Detailed Outlier Investigation</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Outlier ID</th>
                    <th className="text-left p-2">Customer ID</th>
                    <th className="text-left p-2">Amount</th>
                    <th className="text-left p-2">Normal Range</th>
                    <th className="text-left p-2">Deviation</th>
                    <th className="text-left p-2">Risk Level</th>
                    <th className="text-left p-2">Timestamp</th>
                    <th className="text-left p-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {dummyOutlierDetails.map((outlier, index) => (
                    <tr key={outlier.id} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{outlier.id}</td>
                      <td className="p-2">{outlier.customerId}</td>
                      <td className="p-2 font-semibold">{formatCurrency(outlier.amount)}</td>
                      <td className="p-2 text-sm text-muted-foreground">{outlier.normalRange}</td>
                      <td className="p-2">
                        <span className={`font-medium ${outlier.deviation > 0 ? 'text-red-600' : 'text-blue-600'}`}>
                          {outlier.deviation > 0 ? '+' : ''}{formatCurrency(outlier.deviation)}
                        </span>
                      </td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          outlier.riskLevel === 'Critical' ? 'bg-red-100 text-red-800' :
                          outlier.riskLevel === 'High' ? 'bg-orange-100 text-orange-800' :
                          outlier.riskLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {outlier.riskLevel}
                        </span>
                      </td>
                      <td className="p-2 text-sm text-muted-foreground">{outlier.timestamp}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          outlier.status === 'Under Review' ? 'bg-blue-100 text-blue-800' :
                          outlier.status === 'Investigated' ? 'bg-purple-100 text-purple-800' :
                          outlier.status === 'Cleared' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {outlier.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Outlier Management Recommendations */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Outlier Management & Response Guidelines</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-red-600">Critical Risk Response</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Immediate transaction review and verification</li>
                  <li>• Contact customer for transaction confirmation</li>
                  <li>• Escalate to fraud prevention team if suspicious</li>
                  <li>• Document investigation findings and actions taken</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">High Risk Response</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Priority investigation within 24 hours</li>
                  <li>• Review customer transaction history</li>
                  <li>• Check for patterns or unusual behavior</li>
                  <li>• Consider temporary monitoring or limits</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-yellow-600">Medium Risk Response</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Standard review process within 48 hours</li>
                  <li>• Analyze transaction context and timing</li>
                  <li>• Compare with customer's normal behavior</li>
                  <li>• Update customer risk profile if needed</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Low Risk Response</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Routine monitoring and periodic review</li>
                  <li>• Log for trend analysis and reporting</li>
                  <li>• Consider adjusting detection thresholds</li>
                  <li>• Use for machine learning model improvement</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
