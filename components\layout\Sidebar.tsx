"use client";
// Num of tellers, total transaction, volume and value,
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  BarChart3,
  <PERSON>,
  <PERSON><PERSON>hart,
  AlertTriangle,
  Building2,
  Building,
  Home,
  Star,
  Activity,
  Award
} from "lucide-react";
import { useAuth } from "@/lib/auth/context";

import {
  Sidebar as ShadcnSidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton
} from "@/components/ui/sidebar";

// Role-specific navigation items based on AI insights and functionality
const getRoleSpecificItems = (userRole: string) => {
  const roleSpecificItems = {
    agent: [
      {
        title: "Agent Dashboard",
        href: "/agent/dashboard",
        icon: Building2,
      },

    ],
    merchant: [
      {
        title: "Overview",
        href: "/",
        icon: Home,
      },
      // General analytics for merchants
      {
        title: "High-Value Customers",
        href: "/high-value-customers",
        icon: Users,
      },
      {
        title: "Transaction Volume",
        href: "/transaction-volume",
        icon: BarChart3,
      },
      {
        title: "Customer Segmentation",
        href: "/segmentation",
        icon: PieChart,
      },
      {
        title: "Alerts",
        href: "/alerts",
        icon: AlertTriangle,
      }
    ],
    merchant_admin: [
      {
        title: "Overview",
        href: "/merchant-admin/dashboard",
        icon: Home,
      },
      {
        title: "High-Value Customer Identification",
        href: "/merchant-admin/high-value-customers",
        icon: Star,
      },
      {
        title: "Peak Transaction Periods",
        href: "/merchant-admin/peak-periods",
        icon: BarChart3,
      },
      {
        title: "Time Between Transactions Analysis",
        href: "/merchant-admin/transaction-timing",
        icon: Activity,
      },
      {
        title: "Customer Segmentation",
        href: "/merchant-admin/customer-segmentation",
        icon: PieChart,
      },
    ],
    branch_admin: [
      {
        title: "Overview",
        href: "/branch-admin/dashboard",
        icon: Home,
      },
      {
        title: "High-Value Customer Identification",
        href: "/branch-admin/high-value-customers",
        icon: Star,
      },
      {
        title: "Peak Transaction Periods",
        href: "/branch-admin/peak-periods",
        icon: BarChart3,
      },
      {
        title: "Time Between Transactions Analysis",
        href: "/branch-admin/transaction-timing",
        icon: Activity,
      },
      {
        title: "Customer Segmentation",
        href: "/branch-admin/customer-segmentation",
        icon: PieChart,
      },
      {
        title: "Teller Performance",
        href: "/branch-admin/teller-performance",
        icon: Award,
      },
    ],
    teller: [
      {
        title: "Dashboard",
        href: "/teller/dashboard",
        icon: Home,
      },
      {
        title: "Personal Sales",
        href: "/teller/personal-sales",
        icon: BarChart3,
      },
      {
        title: "Fraud Alerts",
        href: "/teller/fraud-alerts",
        icon: AlertTriangle,
      },
    ],
  };

  return roleSpecificItems[userRole as keyof typeof roleSpecificItems] || [];
};

export function Sidebar() {
  const pathname = usePathname();
  const { user } = useAuth();

  if (!user) return null;

  const sidebarItems = getRoleSpecificItems(user.role);

  return (
    <ShadcnSidebar collapsible="icon" className="border-r border-sidebar-border">
      <SidebarHeader className="p-4">
        <h2 className="text-xl font-bold text-sidebar-foreground"></h2>
      </SidebarHeader>
      <SidebarContent className="px-2 py-4">
        <SidebarMenu className="space-y-1">
          {sidebarItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <SidebarMenuButton
                asChild
                isActive={pathname === item.href}
                tooltip={item.title}
              >
                <Link href={item.href}>
                  <item.icon className="h-4 w-4" />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
    </ShadcnSidebar>
  );
}
