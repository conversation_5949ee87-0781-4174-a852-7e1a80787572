"use client";

import { useState } from "react";
import {
  Star,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Activity,
  Crown
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for enhanced customer analysis
const dummyCustomerTrends = [
  { month: "Jan", topCustomerSpend: 15000, avgCustomerSpend: 850 },
  { month: "Feb", topCustomerSpend: 18000, avgCustomerSpend: 920 },
  { month: "Mar", topCustomerSpend: 16500, avgCustomerSpend: 880 },
  { month: "Apr", topCustomerSpend: 22000, avgCustomerSpend: 950 },
  { month: "May", topCustomerSpend: 25000, avgCustomerSpend: 1020 },
  { month: "Jun", topCustomerSpend: 23500, avgCustomerSpend: 980 }
];

const dummyCustomerCategories = [
  { category: "VIP Elite", count: 5, totalSpend: 125000, avgSpend: 25000 },
  { category: "Premium", count: 15, totalSpend: 180000, avgSpend: 12000 },
  { category: "Gold", count: 25, totalSpend: 150000, avgSpend: 6000 },
  { category: "Silver", count: 45, totalSpend: 135000, avgSpend: 3000 },
  { category: "Regular", count: 155, totalSpend: 155000, avgSpend: 1000 }
];

const dummyCustomerBehavior = [
  { behavior: "Frequency", score: 85 },
  { behavior: "Loyalty", score: 92 },
  { behavior: "Value", score: 88 },
  { behavior: "Engagement", score: 76 },
  { behavior: "Retention", score: 89 },
  { behavior: "Referrals", score: 72 }
];

const dummyTopCustomerDetails = [
  {
    customerId: "CUST-001",
    name: "Premium Customer A",
    totalSpend: 45000,
    transactions: 125,
    avgTransaction: 360,
    lastTransaction: "2024-01-15",
    category: "VIP Elite",
    loyaltyScore: 95
  },
  {
    customerId: "CUST-002",
    name: "Premium Customer B",
    totalSpend: 38000,
    transactions: 98,
    avgTransaction: 388,
    lastTransaction: "2024-01-14",
    category: "VIP Elite",
    loyaltyScore: 92
  },
  {
    customerId: "CUST-003",
    name: "Premium Customer C",
    totalSpend: 32000,
    transactions: 85,
    avgTransaction: 376,
    lastTransaction: "2024-01-13",
    category: "Premium",
    loyaltyScore: 88
  },
  {
    customerId: "CUST-004",
    name: "Premium Customer D",
    totalSpend: 28000,
    transactions: 72,
    avgTransaction: 389,
    lastTransaction: "2024-01-12",
    category: "Premium",
    loyaltyScore: 85
  },
  {
    customerId: "CUST-005",
    name: "Premium Customer E",
    totalSpend: 25000,
    transactions: 68,
    avgTransaction: 368,
    lastTransaction: "2024-01-11",
    category: "Premium",
    loyaltyScore: 82
  }
];

// Utility functions for data transformation
const transformTopCustomersData = (topCustomers: any) => {
  if (!topCustomers?.data) return [];

  return topCustomers.data.map((customer: any, index: number) => ({
    id: customer.customer_id,
    customerId: customer.customer_id,
    amount: customer.amount,
    formattedAmount: formatCurrency(customer.amount),
    merchantId: customer.merchant_id,
    rank: index + 1
  }));
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

export default function TopCustomersPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform API data
  const topCustomersData = overviewData ? transformTopCustomersData(overviewData.top_customers) : [];

  // Calculate KPIs from top customers data
  const totalTopCustomerSpend = topCustomersData.reduce((sum, customer) => sum + customer.amount, 0);
  const avgTopCustomerSpend = topCustomersData.length > 0 ? totalTopCustomerSpend / topCustomersData.length : 0;
  const topCustomerCount = topCustomersData.length;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Top Customers Analysis</h2>
        <p className="text-muted-foreground">
          Detailed analysis of high-value customers and their spending patterns
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load customer data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No top customer data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Top Customers"
          value={isLoading ? "..." : error ? "0" : topCustomerCount.toString()}
          icon={Crown}
          trend={{ value: 8.3, isPositive: true }}
        />
        <KpiCard
          title="Total Top Customer Spend"
          value={isLoading ? "..." : error ? formatCurrency(0) : formatCurrency(totalTopCustomerSpend)}
          icon={DollarSign}
          trend={{ value: 15.7, isPositive: true }}
        />
        <KpiCard
          title="Average Top Customer Spend"
          value={isLoading ? "..." : error ? formatCurrency(0) : formatCurrency(avgTopCustomerSpend)}
          icon={TrendingUp}
          trend={{ value: 12.4, isPositive: true }}
        />
        <KpiCard
          title="VIP Elite Customers"
          value="5"
          icon={Star}
          trend={{ value: 25.0, isPositive: true }}
        />
      </div>

      {/* Customer Spending Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Top Customer Spending Trends">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummyCustomerTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Spend"]} />
              <Line type="monotone" dataKey="topCustomerSpend" stroke={COLORS[0]} strokeWidth={3} name="Top Customer Spend" />
              <Line type="monotone" dataKey="avgCustomerSpend" stroke={COLORS[1]} strokeWidth={3} name="Average Customer Spend" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Customer Category Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyCustomerCategories}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "totalSpend" ? formatCurrency(Number(value)) : value,
                name === "totalSpend" ? "Total Spend" : name === "count" ? "Customer Count" : "Avg Spend"
              ]} />
              <Bar dataKey="totalSpend" fill={COLORS[2]} name="Total Spend" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Behavior Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Behavior Analysis">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={dummyCustomerBehavior}>
              <PolarGrid />
              <PolarAngleAxis dataKey="behavior" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar name="Score" dataKey="score" stroke={COLORS[3]} fill={COLORS[3]} fillOpacity={0.3} />
              <Tooltip formatter={(value) => [`${value}%`, "Score"]} />
            </RadarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Customer Category Breakdown">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyCustomerCategories}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ category, count }) => `${category}: ${count}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {dummyCustomerCategories.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Customers"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Top Customers Table */}
      <div>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="ml-2">Loading customer data...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
              {error.message?.includes('No data after filtering')
                ? "No customer data available for the selected time period"
                : `Error loading customer data: ${error.message}`
              }
            </div>
          </div>
        ) : (
          <DataTable
            title="Top Customers by Amount"
            columns={[
              { key: "rank", title: "Rank" },
              { key: "customerId", title: "Customer ID" },
              { key: "formattedAmount", title: "Total Amount" },
            ]}
            data={topCustomersData}
          />
        )}
      </div>

      {/* Detailed Customer Analysis */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Premium Customer Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Customer ID</th>
                    <th className="text-left p-2">Category</th>
                    <th className="text-left p-2">Total Spend</th>
                    <th className="text-left p-2">Transactions</th>
                    <th className="text-left p-2">Avg Transaction</th>
                    <th className="text-left p-2">Loyalty Score</th>
                    <th className="text-left p-2">Last Transaction</th>
                  </tr>
                </thead>
                <tbody>
                  {dummyTopCustomerDetails.map((customer, index) => (
                    <tr key={customer.customerId} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{customer.customerId}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          customer.category === 'VIP Elite' ? 'bg-purple-100 text-purple-800' :
                          customer.category === 'Premium' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {customer.category}
                        </span>
                      </td>
                      <td className="p-2">{formatCurrency(customer.totalSpend)}</td>
                      <td className="p-2">{customer.transactions}</td>
                      <td className="p-2">{formatCurrency(customer.avgTransaction)}</td>
                      <td className="p-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-12 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{ width: `${customer.loyaltyScore}%` }}
                            ></div>
                          </div>
                          <span className="text-sm">{customer.loyaltyScore}%</span>
                        </div>
                      </td>
                      <td className="p-2 text-sm text-muted-foreground">{customer.lastTransaction}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Insights */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Customer Insights & Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">VIP Elite Customers</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Highest value customers with exceptional loyalty scores</li>
                  <li>• Provide white-glove service and exclusive benefits</li>
                  <li>• Regular check-ins and personalized offers</li>
                  <li>• Priority support and dedicated account management</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Premium Customers</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Strong potential for VIP Elite upgrade</li>
                  <li>• Focus on increasing transaction frequency</li>
                  <li>• Offer premium services and early access to new products</li>
                  <li>• Monitor for any decline in spending patterns</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Retention Strategy</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Implement loyalty rewards program</li>
                  <li>• Regular customer satisfaction surveys</li>
                  <li>• Personalized communication and offers</li>
                  <li>• Track customer lifecycle and engagement metrics</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Growth Opportunities</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Cross-sell and upsell relevant products</li>
                  <li>• Referral programs for top customers</li>
                  <li>• Analyze spending patterns for new service opportunities</li>
                  <li>• Regular business reviews with high-value customers</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
