"use client";

import { useState } from "react";
import {
  DollarSign,
  Activity,
  TrendingUp,
  BarChart3,
  AlertTriangle
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  ComposedChart,
  Area,
  AreaChart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for components without API support
const dummyHourlyData = [
  { hour: "00:00", volume: 1200, count: 15, avgValue: 80 },
  { hour: "01:00", volume: 800, count: 10, avgValue: 80 },
  { hour: "02:00", volume: 600, count: 8, avgValue: 75 },
  { hour: "03:00", volume: 400, count: 5, avgValue: 80 },
  { hour: "04:00", volume: 300, count: 4, avgValue: 75 },
  { hour: "05:00", volume: 500, count: 7, avgValue: 71 },
  { hour: "06:00", volume: 1500, count: 20, avgValue: 75 },
  { hour: "07:00", volume: 2800, count: 35, avgValue: 80 },
  { hour: "08:00", volume: 4200, count: 52, avgValue: 81 },
  { hour: "09:00", volume: 5800, count: 72, avgValue: 81 },
  { hour: "10:00", volume: 6500, count: 80, avgValue: 81 },
  { hour: "11:00", volume: 7200, count: 88, avgValue: 82 },
  { hour: "12:00", volume: 8100, count: 95, avgValue: 85 },
  { hour: "13:00", volume: 7800, count: 92, avgValue: 85 },
  { hour: "14:00", volume: 7500, count: 90, avgValue: 83 },
  { hour: "15:00", volume: 6800, count: 85, avgValue: 80 },
  { hour: "16:00", volume: 6200, count: 78, avgValue: 79 },
  { hour: "17:00", volume: 5500, count: 70, avgValue: 79 },
  { hour: "18:00", volume: 4800, count: 60, avgValue: 80 },
  { hour: "19:00", volume: 3200, count: 40, avgValue: 80 },
  { hour: "20:00", volume: 2400, count: 30, avgValue: 80 },
  { hour: "21:00", volume: 1800, count: 22, avgValue: 82 },
  { hour: "22:00", volume: 1200, count: 15, avgValue: 80 },
  { hour: "23:00", volume: 900, count: 12, avgValue: 75 }
];

const dummyWeeklyData = [
  { day: "Monday", volume: 45000, count: 580, avgValue: 78 },
  { day: "Tuesday", volume: 52000, count: 650, avgValue: 80 },
  { day: "Wednesday", volume: 48000, count: 600, avgValue: 80 },
  { day: "Thursday", volume: 55000, count: 690, avgValue: 80 },
  { day: "Friday", volume: 62000, count: 775, avgValue: 80 },
  { day: "Saturday", volume: 38000, count: 475, avgValue: 80 },
  { day: "Sunday", volume: 28000, count: 350, avgValue: 80 }
];

const dummyTransactionTypes = [
  { type: "Cash Deposit", volume: 125000, count: 1250, percentage: 35 },
  { type: "Cash Withdrawal", volume: 98000, count: 1400, percentage: 28 },
  { type: "Transfer", volume: 87000, count: 870, percentage: 25 },
  { type: "Bill Payment", volume: 42000, count: 600, percentage: 12 }
];

// Utility functions for data transformation
const transformTransactionVolumeData = (transactionVolume: any) => {
  if (!transactionVolume?.data?.labels || !transactionVolume?.data?.values) return [];

  return transactionVolume.data.labels.map((label: string, index: number) => ({
    month: label,
    volume: transactionVolume.data.values[index] || 0
  }));
};

const transformTransactionCountData = (transactionCount: any) => {
  if (!transactionCount?.data?.labels || !transactionCount?.data?.values) return [];

  return transactionCount.data.labels.map((label: string, index: number) => ({
    month: label,
    count: transactionCount.data.values[index] || 0
  }));
};

const transformAverageTransactionData = (averageTransactions: any) => {
  if (!averageTransactions?.data?.labels || !averageTransactions?.data?.values) return [];

  return averageTransactions.data.labels.map((label: string, index: number) => ({
    month: label,
    avgValue: averageTransactions.data.values[index] || 0
  }));
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

// Helper function to get KPI value with proper error handling
const getKpiValue = (isLoading: boolean, error: any, value: number | string | undefined, fallback: string = "0") => {
  if (isLoading) return "...";
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return fallback;
    }
    return "Error";
  }
  return value?.toString() || fallback;
};

export default function TransactionAnalyticsPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform API data
  const transactionVolumeData = overviewData ? transformTransactionVolumeData(overviewData.transaction_volume) : [];
  const transactionCountData = overviewData ? transformTransactionCountData(overviewData.transaction_count) : [];
  const averageTransactionData = overviewData ? transformAverageTransactionData(overviewData.average_transactions) : [];

  // Extract stats for KPI cards
  const statsKpis = overviewData?.stats ? {
    totalTransactionValue: overviewData.stats.find(s => s.metric.startsWith('Total Transaction Value'))?.value || 0,
    averageTransactionValue: overviewData.stats.find(s => s.metric.startsWith('Average Transaction Value'))?.value || 0,
    transactionCount: overviewData.stats.find(s => s.metric.startsWith('Transaction Count'))?.value || 0,
    minTransactionValue: overviewData.stats.find(s => s.metric.startsWith('Min Transaction Value'))?.value || 0,
    maxTransactionValue: overviewData.stats.find(s => s.metric.startsWith('Max Transaction Value'))?.value || 0,
  } : null;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Transaction Analytics</h2>
        <p className="text-muted-foreground">
          Detailed analysis of transaction volumes, patterns, and trends
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load transaction data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No transaction data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
        <KpiCard
          title="Total Transaction Value"
          value={(() => {
            const rawValue = getKpiValue(isLoading, error, statsKpis?.totalTransactionValue, "0");
            return rawValue === "..." || rawValue === "Error" || rawValue === "0" ? rawValue : formatCurrency(Number(rawValue));
          })()}
          icon={DollarSign}
          trend={{ value: 15.3, isPositive: true }}
        />
        <KpiCard
          title="Total Transactions"
          value={getKpiValue(isLoading, error, statsKpis?.transactionCount, "0")}
          icon={Activity}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Average Transaction"
          value={(() => {
            const rawValue = getKpiValue(isLoading, error, statsKpis?.averageTransactionValue, "0");
            return rawValue === "..." || rawValue === "Error" || rawValue === "0" ? rawValue : formatCurrency(Number(rawValue));
          })()}
          icon={TrendingUp}
          trend={{ value: 6.8, isPositive: true }}
        />
        <KpiCard
          title="Min Transaction"
          value={(() => {
            const rawValue = getKpiValue(isLoading, error, statsKpis?.minTransactionValue, "0");
            return rawValue === "..." || rawValue === "Error" || rawValue === "0" ? rawValue : formatCurrency(Number(rawValue));
          })()}
          icon={BarChart3}
          trend={{ value: 2.1, isPositive: false }}
        />
        <KpiCard
          title="Max Transaction"
          value={(() => {
            const rawValue = getKpiValue(isLoading, error, statsKpis?.maxTransactionValue, "0");
            return rawValue === "..." || rawValue === "Error" || rawValue === "0" ? rawValue : formatCurrency(Number(rawValue));
          })()}
          icon={TrendingUp}
          trend={{ value: 12.4, isPositive: true }}
        />
      </div>

      {/* Main Transaction Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Monthly Transaction Volume">
          {(() => {
            const state = getChartState(isLoading, error, transactionVolumeData.length > 0, "transaction volume data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={transactionVolumeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Volume"]} />
                    <Bar dataKey="volume" fill={COLORS[0]} name="Transaction Volume" />
                  </BarChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Monthly Transaction Count">
          {(() => {
            const state = getChartState(isLoading, error, transactionCountData.length > 0, "transaction count data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={transactionCountData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="count" stroke={COLORS[1]} strokeWidth={3} name="Transaction Count" />
                  </LineChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>
      </div>

      {/* Average Transaction Value & Combined Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Average Transaction Value Trend">
          {(() => {
            const state = getChartState(isLoading, error, averageTransactionData.length > 0, "average transaction data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={averageTransactionData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Avg Value"]} />
                    <Area type="monotone" dataKey="avgValue" stroke={COLORS[2]} fill={COLORS[2]} fillOpacity={0.3} name="Average Value" />
                  </AreaChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Volume vs Count Comparison">
          {(() => {
            const hasData = transactionVolumeData.length > 0 && transactionCountData.length > 0;
            const state = getChartState(isLoading, error, hasData, "comparison data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={transactionVolumeData.map((item: any, index: number) => ({
                    ...item,
                    count: transactionCountData[index]?.count || 0
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === "volume" ? formatCurrency(Number(value)) : value,
                        name === "volume" ? "Transaction Volume" : "Transaction Count"
                      ]}
                    />
                    <Bar yAxisId="left" dataKey="volume" fill={COLORS[0]} name="Volume" />
                    <Line yAxisId="right" type="monotone" dataKey="count" stroke={COLORS[1]} strokeWidth={3} name="Count" />
                  </ComposedChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>
      </div>

      {/* Hourly and Weekly Patterns (Dummy Data) */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Hourly Transaction Pattern">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={dummyHourlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="hour"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip
                formatter={(value, name) => [
                  name === "volume" ? formatCurrency(Number(value)) : value,
                  name === "volume" ? "Volume" : name === "count" ? "Count" : "Avg Value"
                ]}
              />
              <Area type="monotone" dataKey="volume" stroke={COLORS[3]} fill={COLORS[3]} fillOpacity={0.3} name="Volume" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Weekly Transaction Pattern">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyWeeklyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Volume"]} />
              <Bar dataKey="volume" fill={COLORS[4]} name="Weekly Volume" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Transaction Type Analysis */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Transaction Type Breakdown</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              {dummyTransactionTypes.map((type, index) => (
                <div key={type.type} className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold" style={{ color: COLORS[index] }}>
                    {formatCurrency(type.volume)}
                  </div>
                  <div className="text-sm text-muted-foreground">{type.type}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {type.count} transactions ({type.percentage}%)
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
