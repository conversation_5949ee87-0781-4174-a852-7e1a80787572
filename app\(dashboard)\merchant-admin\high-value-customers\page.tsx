"use client";

import { useState } from "react";
import {
  Star,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Activity,
  Crown,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  <PERSON>att<PERSON><PERSON>,
  <PERSON>att<PERSON>,
  Composed<PERSON>hart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for enhanced high-value customer analysis
const dummyTopSpendersByBranch = [
  { branchName: "Main Branch", topSpender: "CUST-001", amount: 45000, transactions: 125, avgTransaction: 360 },
  { branchName: "Downtown Branch", topSpender: "CUST-045", amount: 38000, transactions: 98, avgTransaction: 388 },
  { branchName: "Suburban Branch", topSpender: "CUST-089", amount: 32000, transactions: 85, avgTransaction: 376 },
  { branchName: "Mall Branch", topSpender: "CUST-123", amount: 28000, transactions: 72, avgTransaction: 389 },
  { branchName: "Airport Branch", topSpender: "CUST-156", amount: 25000, transactions: 68, avgTransaction: 368 }
];

const dummySpendingTrends = [
  { month: "Jan", highValue: 285000, mediumValue: 180000, totalCustomers: 450 },
  { month: "Feb", highValue: 310000, mediumValue: 195000, totalCustomers: 485 },
  { month: "Mar", highValue: 295000, mediumValue: 188000, totalCustomers: 470 },
  { month: "Apr", highValue: 325000, mediumValue: 205000, totalCustomers: 510 },
  { month: "May", highValue: 340000, mediumValue: 220000, totalCustomers: 535 },
  { month: "Jun", highValue: 355000, mediumValue: 235000, totalCustomers: 560 }
];

const dummyCustomerTiers = [
  { tier: "VIP Elite (₵10,000+)", count: 15, totalSpend: 285000, avgSpend: 19000, color: COLORS[0] },
  { tier: "Premium (₵5,000-₵9,999)", count: 35, totalSpend: 245000, avgSpend: 7000, color: COLORS[1] },
  { tier: "Gold (₵2,000-₵4,999)", count: 85, totalSpend: 255000, avgSpend: 3000, color: COLORS[2] },
  { tier: "Silver (₵500-₵1,999)", count: 165, totalSpend: 165000, avgSpend: 1000, color: COLORS[3] }
];

const dummyFrequencyVsValue = [
  { customerId: "CUST-001", frequency: 25, totalSpend: 45000, avgTransaction: 1800 },
  { customerId: "CUST-002", frequency: 22, totalSpend: 38000, avgTransaction: 1727 },
  { customerId: "CUST-003", frequency: 20, totalSpend: 32000, avgTransaction: 1600 },
  { customerId: "CUST-004", frequency: 18, totalSpend: 28000, avgTransaction: 1556 },
  { customerId: "CUST-005", frequency: 16, totalSpend: 25000, avgTransaction: 1563 },
  { customerId: "CUST-006", frequency: 15, totalSpend: 22000, avgTransaction: 1467 },
  { customerId: "CUST-007", frequency: 14, totalSpend: 20000, avgTransaction: 1429 },
  { customerId: "CUST-008", frequency: 12, totalSpend: 18000, avgTransaction: 1500 },
  { customerId: "CUST-009", frequency: 10, totalSpend: 15000, avgTransaction: 1500 },
  { customerId: "CUST-010", frequency: 8, totalSpend: 12000, avgTransaction: 1500 }
];

// Utility functions for data transformation
const transformTopCustomersData = (topCustomers: any) => {
  if (!topCustomers?.data) return [];

  return topCustomers.data.map((customer: any, index: number) => ({
    id: customer.customer_id,
    customerId: customer.customer_id,
    amount: customer.amount,
    formattedAmount: formatCurrency(customer.amount),
    merchantId: customer.merchant_id,
    rank: index + 1,
    tier: customer.amount >= 10000 ? "VIP Elite" :
          customer.amount >= 5000 ? "Premium" :
          customer.amount >= 2000 ? "Gold" : "Silver"
  }));
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

export default function HighValueCustomersPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 20, // Get more customers for high-value analysis
    ...apiParams
  });

  // Transform API data
  const topCustomersData = overviewData ? transformTopCustomersData(overviewData.top_customers) : [];

  // Calculate KPIs from top customers data
  const totalHighValueCustomers = topCustomersData.filter((customer: { amount: number; }) => customer.amount >= 5000).length;
  const totalHighValueSpend = topCustomersData.filter((customer: { amount: number; }) => customer.amount >= 5000).reduce((sum: any, customer: { amount: any; }) => sum + customer.amount, 0);
  const avgHighValueSpend = totalHighValueCustomers > 0 ? totalHighValueSpend / totalHighValueCustomers : 0;
  const vipEliteCustomers = topCustomersData.filter((customer: { amount: number; }) => customer.amount >= 10000).length;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">High-Value Customer Identification</h2>
        <p className="text-muted-foreground">
          Identify customers who frequently transact or spend significantly more than average across all branches
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load customer data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No high-value customer data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="High-Value Customers"
          value={isLoading ? "..." : error ? "0" : totalHighValueCustomers.toString()}
          icon={Star}
          trend={{ value: 18.3, isPositive: true }}
        />
        <KpiCard
          title="% from Top Customers"
          value={isLoading ? "..." : error ? "0" : vipEliteCustomers.toString()}
          icon={Crown}
          trend={{ value: 25.0, isPositive: true }}
        />
        <KpiCard
          title="High-Value Total Spend"
          value={isLoading ? "..." : error ? formatCurrency(0) : formatCurrency(totalHighValueSpend)}
          icon={DollarSign}
          trend={{ value: 22.7, isPositive: true }}
        />
        <KpiCard
          title="Avg High-Value Spend"
          value={isLoading ? "..." : error ? formatCurrency(0) : formatCurrency(avgHighValueSpend)}
          icon={Target}
          trend={{ value: 15.4, isPositive: true }}
        />
      </div>

      {/* High-Value Customer Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Top Spenders by Branch">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyTopSpendersByBranch}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="branchName"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Amount"]} />
              <Bar dataKey="amount" fill={COLORS[0]} name="Top Spender Amount" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="High-Value Spending Trends">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummySpendingTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Spend"]} />
              <Line type="monotone" dataKey="highValue" stroke={COLORS[0]} strokeWidth={3} name="High-Value Spend" />
              <Line type="monotone" dataKey="mediumValue" stroke={COLORS[1]} strokeWidth={3} name="Medium-Value Spend" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Tiers & Frequency Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Value Tiers">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyCustomerTiers}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ tier, count }) => `${count} customers`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {dummyCustomerTiers.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Customers"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Transaction Frequency vs Value">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart data={dummyFrequencyVsValue}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="frequency" name="Frequency" />
              <YAxis dataKey="totalSpend" name="Total Spend" />
              <Tooltip
                formatter={(value, name) => [
                  name === "totalSpend" ? formatCurrency(Number(value)) : value,
                  name === "totalSpend" ? "Total Spend" : "Transaction Frequency"
                ]}
              />
              <Scatter dataKey="totalSpend" fill={COLORS[2]} />
            </ScatterChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Tier Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Tier Revenue Contribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyCustomerTiers}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="tier"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Total Spend"]} />
              <Bar dataKey="totalSpend" fill={COLORS[3]} name="Total Spend" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Average Spend by Tier">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={dummyCustomerTiers}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="tier"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "count" ? value : formatCurrency(Number(value)),
                name === "count" ? "Customer Count" : "Avg Spend"
              ]} />
              <Bar dataKey="count" fill={COLORS[4]} name="Customer Count" />
              <Line type="monotone" dataKey="avgSpend" stroke={COLORS[5]} strokeWidth={3} name="Avg Spend" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Top Customers Table */}
      <div>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="ml-2">Loading customer data...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
              {error.message?.includes('No data after filtering')
                ? "No customer data available for the selected time period"
                : `Error loading customer data: ${error.message}`
              }
            </div>
          </div>
        ) : (
          <DataTable
            title="High-Value Customers Ranking"
            columns={[
              { key: "rank", title: "Rank" },
              { key: "customerId", title: "Customer ID" },
              { key: "formattedAmount", title: "Total Amount" },
              { key: "tier", title: "Tier" },
            ]}
            data={topCustomersData}
          />
        )}
      </div>

      {/* Top Spenders by Branch Analysis */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Top Spenders Across All Branches</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Branch</th>
                    <th className="text-left p-2">Top Customer</th>
                    <th className="text-left p-2">Total Spend</th>
                    <th className="text-left p-2">Transactions</th>
                    <th className="text-left p-2">Avg Transaction</th>
                    <th className="text-left p-2">Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {dummyTopSpendersByBranch.map((branch, index) => (
                    <tr key={branch.branchName} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{branch.branchName}</td>
                      <td className="p-2">{branch.topSpender}</td>
                      <td className="p-2 font-semibold">{formatCurrency(branch.amount)}</td>
                      <td className="p-2">{branch.transactions}</td>
                      <td className="p-2">{formatCurrency(branch.avgTransaction)}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          index === 0 ? 'bg-green-100 text-green-800' :
                          index === 1 ? 'bg-blue-100 text-blue-800' :
                          index === 2 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {index === 0 ? 'Excellent' : index === 1 ? 'Very Good' : index === 2 ? 'Good' : 'Average'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* High-Value Customer Insights */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>High-Value Customer Strategy & Insights</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">VIP Elite Customers (₵10,000+)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Provide dedicated account management and white-glove service</li>
                  <li>• Offer exclusive products and early access to new services</li>
                  <li>• Regular business reviews and personalized financial solutions</li>
                  <li>• Priority support and expedited transaction processing</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Premium Customers (₵5,000-₵9,999)</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Target for VIP Elite upgrade through enhanced services</li>
                  <li>• Offer premium banking packages and investment options</li>
                  <li>• Provide relationship manager for personalized service</li>
                  <li>• Monitor transaction patterns for upselling opportunities</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Identification Strategies</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Track customers with consistent high-value transactions</li>
                  <li>• Monitor frequency patterns and spending behaviors</li>
                  <li>• Identify customers showing upward spending trends</li>
                  <li>• Cross-reference with business account holders</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Retention & Growth</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Implement loyalty rewards and cashback programs</li>
                  <li>• Regular satisfaction surveys and feedback collection</li>
                  <li>• Proactive communication about new products and services</li>
                  <li>• Monitor for any decline in transaction volume or value</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
